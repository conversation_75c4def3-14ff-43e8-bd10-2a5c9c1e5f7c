# Hướng dẫn sử dụng WebSocket với STOMP/SockJS

## Tổng quan

Dự án đã được setup với hệ thống WebSocket sử dụng STOMP/SockJS để xử lý realtime communication. Hệ thống bao gồm:

- **useWebSocket**: Hook cơ bản để kết nối và quản lý WebSocket
- **useWebSocketUtils**: <PERSON><PERSON>c hook chuyên biệt cho chat, notifications, presence, live data
- **WebSocket Config**: Cấu hình endpoints và settings
- **Demo Components**: V<PERSON> dụ minh họa cách sử dụng

## Cài đặt

Các dependencies đã được cài đặt:
```bash
npm install @stomp/stompjs sockjs-client
npm install --save-dev @types/sockjs-client
```

## Cấu trúc files

```
src/
├── hooks/
│   ├── useWebSocket.ts          # Hook cơ bản
│   └── useWebSocketUtils.ts     # Hook utilities
├── config/
│   └── websocket.ts             # Cấu hình WebSocket
├── components/
│   └── examples/
│       └── WebSocketDemo.tsx    # Demo component
└── docs/
    └── WEBSOCKET_GUIDE.md       # Hướng dẫn này
```

## Cách sử dụng

### 1. Hook cơ bản - useWebSocket

```tsx
import { useWebSocket } from '@/hooks/useWebSocket';
import { createWebSocketUrl } from '@/config/websocket';

const MyComponent = () => {
  const webSocket = useWebSocket({
    url: createWebSocketUrl(),
    debug: true,
    headers: { Authorization: 'Bearer your-token' }
  });

  // Subscribe to messages
  useEffect(() => {
    const unsubscribe = webSocket.subscribe('/topic/test', (message) => {
      console.log('Received:', message.body);
    });

    return unsubscribe;
  }, [webSocket.subscribe]);

  // Send message
  const sendMessage = () => {
    webSocket.sendMessage('/app/test', { text: 'Hello World!' });
  };

  return (
    <div>
      <div>Status: {webSocket.isConnected ? 'Connected' : 'Disconnected'}</div>
      <button onClick={sendMessage}>Send Message</button>
    </div>
  );
};
```

### 2. Chat Realtime - useWebSocketChat

```tsx
import { useWebSocketChat } from '@/hooks/useWebSocketUtils';
import { createWebSocketUrl } from '@/config/websocket';

const ChatComponent = () => {
  const chat = useWebSocketChat(
    createWebSocketUrl(),
    'room-123',
    'user-456',
    'John Doe'
  );

  const [message, setMessage] = useState('');

  const handleSend = () => {
    chat.sendMessage(message);
    setMessage('');
  };

  return (
    <div>
      <div>Online users: {chat.onlineUsers.length}</div>
      
      <div className="messages">
        {chat.messages.map((msg, index) => (
          <div key={index}>
            <strong>{msg.username}:</strong> {msg.message}
          </div>
        ))}
      </div>

      <input
        value={message}
        onChange={(e) => {
          setMessage(e.target.value);
          chat.sendTyping(e.target.value.length > 0);
        }}
        onKeyPress={(e) => e.key === 'Enter' && handleSend()}
      />
      <button onClick={handleSend}>Send</button>
    </div>
  );
};
```

### 3. Notifications - useWebSocketNotifications

```tsx
import { useWebSocketNotifications } from '@/hooks/useWebSocketUtils';

const NotificationComponent = () => {
  const notifications = useWebSocketNotifications(
    createWebSocketUrl(),
    'user-123'
  );

  return (
    <div>
      <div>Unread: {notifications.unreadCount}</div>
      
      {notifications.notifications.map((notif) => (
        <div key={notif.id} className={notif.read ? 'read' : 'unread'}>
          <h4>{notif.title}</h4>
          <p>{notif.message}</p>
          {!notif.read && (
            <button onClick={() => notifications.markAsRead(notif.id)}>
              Mark as read
            </button>
          )}
        </div>
      ))}
    </div>
  );
};
```

### 4. Live Data Updates - useWebSocketLiveData

```tsx
import { useWebSocketLiveData } from '@/hooks/useWebSocketUtils';

const LiveDataComponent = () => {
  const liveData = useWebSocketLiveData<User>(
    createWebSocketUrl(),
    'users'
  );

  useEffect(() => {
    liveData.requestInitialData();
  }, []);

  return (
    <div>
      <button onClick={liveData.refreshData}>Refresh</button>
      
      {liveData.lastUpdate && (
        <div>
          Last update: {liveData.lastUpdate.type} at {new Date(liveData.lastUpdate.timestamp).toLocaleString()}
        </div>
      )}

      <div>
        {liveData.data.map((user) => (
          <div key={user.id}>{user.name}</div>
        ))}
      </div>
    </div>
  );
};
```

### 5. Presence/Status - useWebSocketPresence

```tsx
import { useWebSocketPresence } from '@/hooks/useWebSocketUtils';

const PresenceComponent = () => {
  const presence = useWebSocketPresence(
    createWebSocketUrl(),
    'room-123',
    { userId: 'user-456', username: 'John Doe', status: 'online' }
  );

  return (
    <div>
      <div>Users online: {presence.userCount}</div>
      
      <select onChange={(e) => presence.updateStatus(e.target.value)}>
        <option value="online">Online</option>
        <option value="away">Away</option>
        <option value="busy">Busy</option>
      </select>

      <div>
        {presence.presenceList.map((user) => (
          <div key={user.userId}>
            {user.username} - {user.status}
          </div>
        ))}
      </div>
    </div>
  );
};
```

## Cấu hình

### Environment Variables

Tạo file `.env.local`:

```env
# WebSocket URLs
NEXT_PUBLIC_WEBSOCKET_URL=ws://localhost:8080/ws
NEXT_PUBLIC_API_URL=http://localhost:8080/api

# Production
# NEXT_PUBLIC_WEBSOCKET_URL=wss://your-domain.com/ws
# NEXT_PUBLIC_API_URL=https://your-domain.com/api
```

### Cấu hình Server (Spring Boot example)

```java
@Configuration
@EnableWebSocketMessageBroker
public class WebSocketConfig implements WebSocketMessageBrokerConfigurer {

    @Override
    public void configureMessageBroker(MessageBrokerRegistry config) {
        config.enableSimpleBroker("/topic", "/user");
        config.setApplicationDestinationPrefixes("/app");
        config.setUserDestinationPrefix("/user");
    }

    @Override
    public void registerStompEndpoints(StompEndpointRegistry registry) {
        registry.addEndpoint("/ws")
                .setAllowedOriginPatterns("*")
                .withSockJS();
    }
}
```

## Endpoints

### Chat
- Subscribe: `/topic/chat/{roomId}`
- Send: `/app/chat/{roomId}`
- Join: `/app/chat/join`
- Leave: `/app/chat/leave`

### Notifications
- Subscribe: `/user/{userId}/notifications`
- Send: `/app/notifications/send`

### Presence
- Subscribe: `/topic/presence/{roomId}`
- Join: `/app/presence/{roomId}/join`
- Update: `/app/presence/{roomId}/update`
- Leave: `/app/presence/{roomId}/leave`

### Live Data
- Subscribe: `/topic/data/{dataType}`
- Request: `/app/data/{dataType}/request`

## Best Practices

1. **Cleanup subscriptions**: Luôn cleanup subscriptions trong useEffect
2. **Error handling**: Xử lý lỗi kết nối và hiển thị trạng thái cho user
3. **Reconnection**: Sử dụng auto-reconnect với exponential backoff
4. **Authentication**: Gửi token trong headers khi kết nối
5. **Message validation**: Validate messages trước khi gửi/nhận

## Troubleshooting

### Lỗi kết nối
- Kiểm tra URL WebSocket server
- Kiểm tra CORS settings
- Kiểm tra firewall/proxy

### Lỗi authentication
- Kiểm tra token trong headers
- Kiểm tra token expiry

### Performance
- Limit số lượng subscriptions
- Cleanup unused subscriptions
- Throttle typing indicators

## Demo

Để xem demo, import và sử dụng component:

```tsx
import WebSocketDemo from '@/components/examples/WebSocketDemo';

export default function DemoPage() {
  return <WebSocketDemo />;
}
```

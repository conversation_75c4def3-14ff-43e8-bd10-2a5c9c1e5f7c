'use client';

import React, { useState, useEffect } from 'react';
import { 
  useWebSocket, 
  useWebSocketSubscription 
} from '@/hooks/useWebSocket';
import { 
  useWebSocketChat, 
  useWebSocketNotifications,
  useWebSocketPresence,
  useWebSocketLiveData 
} from '@/hooks/useWebSocketUtils';
import { 
  createWebSocketUrl, 
  WEBSOCKET_ENDPOINTS,
  getAuthHeaders 
} from '@/config/websocket';

// Demo component cho basic WebSocket
const BasicWebSocketDemo: React.FC = () => {
  const [message, setMessage] = useState('');
  const [destination, setDestination] = useState('/topic/test');

  const webSocket = useWebSocket({
    url: createWebSocketUrl(),
    debug: true,
    headers: getAuthHeaders(),
  });

  const handleSendMessage = () => {
    if (message.trim()) {
      webSocket.sendMessage(destination, { text: message, timestamp: Date.now() });
      setMessage('');
    }
  };

  return (
    <div className="p-4 border rounded-lg">
      <h3 className="text-lg font-semibold mb-4">Basic WebSocket Demo</h3>
      
      <div className="mb-4">
        <div className="flex items-center gap-2 mb-2">
          <span className={`w-3 h-3 rounded-full ${
            webSocket.isConnected ? 'bg-green-500' : 
            webSocket.isConnecting ? 'bg-yellow-500' : 'bg-red-500'
          }`}></span>
          <span className="text-sm">
            {webSocket.isConnected ? 'Đã kết nối' : 
             webSocket.isConnecting ? 'Đang kết nối...' : 'Ngắt kết nối'}
          </span>
        </div>
        
        {webSocket.error && (
          <div className="text-red-500 text-sm mb-2">
            Lỗi: {webSocket.error}
          </div>
        )}
      </div>

      <div className="space-y-2">
        <input
          type="text"
          placeholder="Destination (e.g., /topic/test)"
          value={destination}
          onChange={(e) => setDestination(e.target.value)}
          className="w-full p-2 border rounded"
        />
        
        <div className="flex gap-2">
          <input
            type="text"
            placeholder="Nhập tin nhắn..."
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
            className="flex-1 p-2 border rounded"
          />
          <button
            onClick={handleSendMessage}
            disabled={!webSocket.isConnected || !message.trim()}
            className="px-4 py-2 bg-blue-500 text-white rounded disabled:bg-gray-300"
          >
            Gửi
          </button>
        </div>
      </div>

      {webSocket.lastMessage && (
        <div className="mt-4 p-2 bg-gray-100 rounded">
          <div className="text-sm text-gray-600">Tin nhắn cuối:</div>
          <div className="text-sm">
            <strong>Destination:</strong> {webSocket.lastMessage.destination}
          </div>
          <div className="text-sm">
            <strong>Body:</strong> {JSON.stringify(webSocket.lastMessage.body)}
          </div>
          <div className="text-sm">
            <strong>Time:</strong> {new Date(webSocket.lastMessage.timestamp).toLocaleTimeString()}
          </div>
        </div>
      )}
    </div>
  );
};

// Demo component cho Chat
const ChatDemo: React.FC = () => {
  const [message, setMessage] = useState('');
  const [roomId] = useState('demo-room');
  const [userId] = useState('user-' + Math.random().toString(36).substr(2, 9));
  const [username] = useState('User ' + Math.random().toString(36).substr(2, 5));

  const chat = useWebSocketChat(
    createWebSocketUrl(),
    roomId,
    userId,
    username,
    { debug: true }
  );

  const handleSendMessage = () => {
    if (message.trim()) {
      chat.sendMessage(message);
      setMessage('');
    }
  };

  return (
    <div className="p-4 border rounded-lg">
      <h3 className="text-lg font-semibold mb-4">Chat Demo</h3>
      
      <div className="mb-4">
        <div className="text-sm text-gray-600 mb-2">
          Room: {roomId} | User: {username} | Online: {chat.onlineUsers.length}
        </div>
        
        <div className="flex items-center gap-2 mb-2">
          <span className={`w-3 h-3 rounded-full ${
            chat.isConnected ? 'bg-green-500' : 'bg-red-500'
          }`}></span>
          <span className="text-sm">
            {chat.isConnected ? 'Đã kết nối' : 'Ngắt kết nối'}
          </span>
        </div>
      </div>

      <div className="h-40 overflow-y-auto border rounded p-2 mb-4 bg-gray-50">
        {chat.messages.map((msg, index) => (
          <div key={index} className="mb-2">
            <div className="text-xs text-gray-500">
              {msg.username} - {new Date(msg.timestamp).toLocaleTimeString()}
            </div>
            <div className="text-sm">{msg.message}</div>
          </div>
        ))}
      </div>

      {chat.isTyping.length > 0 && (
        <div className="text-xs text-gray-500 mb-2">
          {chat.isTyping.length} người đang gõ...
        </div>
      )}

      <div className="flex gap-2">
        <input
          type="text"
          placeholder="Nhập tin nhắn..."
          value={message}
          onChange={(e) => {
            setMessage(e.target.value);
            chat.sendTyping(e.target.value.length > 0);
          }}
          onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
          className="flex-1 p-2 border rounded"
        />
        <button
          onClick={handleSendMessage}
          disabled={!chat.isConnected || !message.trim()}
          className="px-4 py-2 bg-blue-500 text-white rounded disabled:bg-gray-300"
        >
          Gửi
        </button>
      </div>
    </div>
  );
};

// Demo component cho Notifications
const NotificationDemo: React.FC = () => {
  const [userId] = useState('user-' + Math.random().toString(36).substr(2, 9));

  const notifications = useWebSocketNotifications(
    createWebSocketUrl(),
    userId,
    { debug: true }
  );

  return (
    <div className="p-4 border rounded-lg">
      <h3 className="text-lg font-semibold mb-4">Notification Demo</h3>
      
      <div className="mb-4">
        <div className="text-sm text-gray-600 mb-2">
          User ID: {userId} | Chưa đọc: {notifications.unreadCount}
        </div>
        
        <div className="flex items-center gap-2 mb-2">
          <span className={`w-3 h-3 rounded-full ${
            notifications.isConnected ? 'bg-green-500' : 'bg-red-500'
          }`}></span>
          <span className="text-sm">
            {notifications.isConnected ? 'Đã kết nối' : 'Ngắt kết nối'}
          </span>
        </div>
      </div>

      <div className="space-y-2 mb-4">
        <button
          onClick={notifications.markAllAsRead}
          disabled={notifications.unreadCount === 0}
          className="px-3 py-1 bg-green-500 text-white rounded text-sm disabled:bg-gray-300"
        >
          Đánh dấu tất cả đã đọc
        </button>
        <button
          onClick={notifications.clearNotifications}
          className="px-3 py-1 bg-red-500 text-white rounded text-sm ml-2"
        >
          Xóa tất cả
        </button>
      </div>

      <div className="h-40 overflow-y-auto border rounded p-2 bg-gray-50">
        {notifications.notifications.map((notif) => (
          <div
            key={notif.id}
            className={`mb-2 p-2 rounded ${
              notif.read ? 'bg-gray-200' : 'bg-blue-100'
            }`}
          >
            <div className="flex justify-between items-start">
              <div>
                <div className="font-semibold text-sm">{notif.title}</div>
                <div className="text-sm">{notif.message}</div>
                <div className="text-xs text-gray-500">
                  {new Date(notif.timestamp).toLocaleString()}
                </div>
              </div>
              {!notif.read && (
                <button
                  onClick={() => notifications.markAsRead(notif.id)}
                  className="text-xs bg-blue-500 text-white px-2 py-1 rounded"
                >
                  Đánh dấu đã đọc
                </button>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Main demo component
const WebSocketDemo: React.FC = () => {
  const [activeTab, setActiveTab] = useState('basic');

  const tabs = [
    { id: 'basic', label: 'Basic WebSocket' },
    { id: 'chat', label: 'Chat' },
    { id: 'notifications', label: 'Notifications' },
  ];

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">WebSocket Demo</h1>
      
      <div className="mb-6">
        <div className="flex space-x-1 border-b">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`px-4 py-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-b-2 border-blue-500 text-blue-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      <div className="space-y-6">
        {activeTab === 'basic' && <BasicWebSocketDemo />}
        {activeTab === 'chat' && <ChatDemo />}
        {activeTab === 'notifications' && <NotificationDemo />}
      </div>
    </div>
  );
};

export default WebSocketDemo;

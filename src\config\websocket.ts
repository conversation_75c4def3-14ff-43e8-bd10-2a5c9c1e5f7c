// WebSocket configuration
export const WEBSOCKET_CONFIG = {
  // Base URLs cho các môi trường khác nhau
  DEVELOPMENT: {
    BASE_URL: 'http://localhost:8080/ws',
    API_URL: 'http://localhost:8080/api',
  },
  PRODUCTION: {
    BASE_URL: 'wss://your-production-domain.com/ws',
    API_URL: 'https://your-production-domain.com/api',
  },
  STAGING: {
    BASE_URL: 'wss://staging.your-domain.com/ws',
    API_URL: 'https://staging.your-domain.com/api',
  },
};

// Lấy config dựa trên environment
export const getWebSocketConfig = () => {
  const env = process.env.NODE_ENV;
  
  switch (env) {
    case 'production':
      return WEBSOCKET_CONFIG.PRODUCTION;
    case 'staging':
      return WEBSOCKET_CONFIG.STAGING;
    default:
      return WEBSOCKET_CONFIG.DEVELOPMENT;
  }
};

// WebSocket endpoints
export const WEBSOCKET_ENDPOINTS = {
  // Chat endpoints
  CHAT: {
    ROOM: (roomId: string) => `/topic/chat/${roomId}`,
    PRIVATE: (userId: string) => `/user/${userId}/chat`,
    SEND_MESSAGE: (roomId: string) => `/app/chat/${roomId}`,
    JOIN_ROOM: '/app/chat/join',
    LEAVE_ROOM: '/app/chat/leave',
  },
  
  // Notification endpoints
  NOTIFICATIONS: {
    USER: (userId: string) => `/user/${userId}/notifications`,
    GLOBAL: '/topic/notifications',
    SEND: '/app/notifications/send',
  },
  
  // Presence endpoints
  PRESENCE: {
    ROOM: (roomId: string) => `/topic/presence/${roomId}`,
    JOIN: (roomId: string) => `/app/presence/${roomId}/join`,
    LEAVE: (roomId: string) => `/app/presence/${roomId}/leave`,
    UPDATE: (roomId: string) => `/app/presence/${roomId}/update`,
  },
  
  // Live data endpoints
  DATA: {
    UPDATES: (dataType: string) => `/topic/data/${dataType}`,
    REQUEST: (dataType: string) => `/app/data/${dataType}/request`,
  },
  
  // Typing indicators
  TYPING: {
    ROOM: (roomId: string) => `/topic/typing/${roomId}`,
    SEND: (roomId: string) => `/app/typing/${roomId}`,
  },
  
  // System events
  SYSTEM: {
    STATUS: '/topic/system/status',
    MAINTENANCE: '/topic/system/maintenance',
  },
};

// Default WebSocket options
export const DEFAULT_WEBSOCKET_OPTIONS = {
  reconnectDelay: 5000,
  heartbeatIncoming: 4000,
  heartbeatOutgoing: 4000,
  debug: process.env.NODE_ENV === 'development',
  headers: {
    'Content-Type': 'application/json',
  },
};

// WebSocket connection states
export const WEBSOCKET_STATES = {
  CONNECTING: 'CONNECTING',
  CONNECTED: 'CONNECTED',
  DISCONNECTED: 'DISCONNECTED',
  ERROR: 'ERROR',
  RECONNECTING: 'RECONNECTING',
} as const;

// Message types
export const MESSAGE_TYPES = {
  CHAT: {
    TEXT: 'text',
    IMAGE: 'image',
    FILE: 'file',
    SYSTEM: 'system',
  },
  NOTIFICATION: {
    INFO: 'info',
    SUCCESS: 'success',
    WARNING: 'warning',
    ERROR: 'error',
  },
  PRESENCE: {
    ONLINE: 'online',
    AWAY: 'away',
    BUSY: 'busy',
    OFFLINE: 'offline',
  },
  DATA: {
    CREATE: 'CREATE',
    UPDATE: 'UPDATE',
    DELETE: 'DELETE',
    SYNC: 'SYNC',
  },
} as const;

// Error codes
export const WEBSOCKET_ERROR_CODES = {
  CONNECTION_FAILED: 'CONNECTION_FAILED',
  AUTHENTICATION_FAILED: 'AUTHENTICATION_FAILED',
  SUBSCRIPTION_FAILED: 'SUBSCRIPTION_FAILED',
  MESSAGE_SEND_FAILED: 'MESSAGE_SEND_FAILED',
  INVALID_DESTINATION: 'INVALID_DESTINATION',
  SERVER_ERROR: 'SERVER_ERROR',
} as const;

// Utility functions
export const createWebSocketUrl = (path: string = '') => {
  const config = getWebSocketConfig();
  return `${config.BASE_URL}${path}`;
};

export const createApiUrl = (path: string = '') => {
  const config = getWebSocketConfig();
  return `${config.API_URL}${path}`;
};

// Authentication headers helper
export const getAuthHeaders = (token?: string) => {
  const headers: Record<string, string> = {
    ...DEFAULT_WEBSOCKET_OPTIONS.headers,
  };
  
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }
  
  return headers;
};

// Room ID generators
export const generateRoomId = (type: string, ...identifiers: string[]) => {
  return `${type}_${identifiers.join('_')}`;
};

export const generateChatRoomId = (participants: string[]) => {
  return generateRoomId('chat', ...participants.sort());
};

export const generatePresenceRoomId = (roomType: string, roomId: string) => {
  return generateRoomId('presence', roomType, roomId);
};

// Message validation
export const validateMessage = (message: any): boolean => {
  return (
    message &&
    typeof message === 'object' &&
    message.destination &&
    message.body !== undefined
  );
};

// Connection retry logic
export const createRetryConfig = (maxRetries: number = 5) => {
  return {
    maxRetries,
    retryDelay: (attempt: number) => Math.min(1000 * Math.pow(2, attempt), 30000),
    shouldRetry: (error: any) => {
      // Retry on network errors, but not on authentication errors
      return !error?.code || error.code !== WEBSOCKET_ERROR_CODES.AUTHENTICATION_FAILED;
    },
  };
};

// Event emitter for global WebSocket events
class WebSocketEventEmitter {
  private listeners: Map<string, Function[]> = new Map();

  on(event: string, callback: Function) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(callback);
  }

  off(event: string, callback: Function) {
    const callbacks = this.listeners.get(event);
    if (callbacks) {
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  emit(event: string, ...args: any[]) {
    const callbacks = this.listeners.get(event);
    if (callbacks) {
      callbacks.forEach(callback => callback(...args));
    }
  }

  removeAllListeners(event?: string) {
    if (event) {
      this.listeners.delete(event);
    } else {
      this.listeners.clear();
    }
  }
}

export const webSocketEventEmitter = new WebSocketEventEmitter();

// Global WebSocket events
export const WEBSOCKET_EVENTS = {
  CONNECTED: 'websocket:connected',
  DISCONNECTED: 'websocket:disconnected',
  ERROR: 'websocket:error',
  MESSAGE_RECEIVED: 'websocket:message_received',
  MESSAGE_SENT: 'websocket:message_sent',
  SUBSCRIPTION_ADDED: 'websocket:subscription_added',
  SUBSCRIPTION_REMOVED: 'websocket:subscription_removed',
} as const;

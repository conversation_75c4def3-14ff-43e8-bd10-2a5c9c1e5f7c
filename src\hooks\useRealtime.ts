'use client';

import { useEffect, useRef, useState, useCallback } from 'react';
import { supabase } from '@/config/supabaseClient';
import { RealtimeChannel, RealtimePostgresChangesPayload } from '@supabase/supabase-js';

// Types cho realtime events
export interface RealtimeEvent<T = any> {
  eventType: 'INSERT' | 'UPDATE' | 'DELETE';
  new: T;
  old: T;
  table: string;
  schema: string;
}

export interface RealtimeOptions {
  table?: string;
  schema?: string;
  filter?: string;
  event?: '*' | 'INSERT' | 'UPDATE' | 'DELETE';
}

export interface RealtimeHookReturn<T = any> {
  data: T[];
  isConnected: boolean;
  error: string | null;
  subscribe: () => void;
  unsubscribe: () => void;
  channel: RealtimeChannel | null;
}

/**
 * Hook chính để xử lý realtime với Supabase
 * @param channelName - Tên channel để subscribe
 * @param options - Các tùy chọn cho realtime
 * @param onEvent - Callback khi có event mới
 */
export const useRealtime = <T = any>(
  channelName: string,
  options: RealtimeOptions = {},
  onEvent?: (event: RealtimeEvent<T>) => void
): RealtimeHookReturn<T> => {
  const [data, setData] = useState<T[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const channelRef = useRef<RealtimeChannel | null>(null);

  const handleEvent = useCallback((payload: RealtimePostgresChangesPayload<T>) => {
    const event: RealtimeEvent<T> = {
      eventType: payload.eventType,
      new: payload.new,
      old: payload.old,
      table: payload.table,
      schema: payload.schema,
    };

    // Gọi callback nếu có
    if (onEvent) {
      onEvent(event);
    }

    // Cập nhật data dựa trên event type
    setData(prevData => {
      switch (payload.eventType) {
        case 'INSERT':
          return [...prevData, payload.new];
        case 'UPDATE':
          return prevData.map(item => 
            (item as any).id === (payload.new as any).id ? payload.new : item
          );
        case 'DELETE':
          return prevData.filter(item => 
            (item as any).id !== (payload.old as any).id
          );
        default:
          return prevData;
      }
    });
  }, [onEvent]);

  const subscribe = useCallback(() => {
    if (channelRef.current) {
      return; // Đã subscribe rồi
    }

    try {
      const channel = supabase.channel(channelName);

      // Subscribe to postgres changes nếu có table
      if (options.table) {
        channel.on(
          'postgres_changes',
          {
            event: options.event || '*',
            schema: options.schema || 'public',
            table: options.table,
            filter: options.filter,
          },
          handleEvent
        );
      }

      // Subscribe to presence changes
      channel.on('presence', { event: 'sync' }, () => {
        console.log('Presence synced');
      });

      // Subscribe to broadcast messages
      channel.on('broadcast', { event: '*' }, (payload) => {
        console.log('Broadcast received:', payload);
      });

      channel.subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          setIsConnected(true);
          setError(null);
          console.log(`✅ Subscribed to channel: ${channelName}`);
        } else if (status === 'CHANNEL_ERROR') {
          setIsConnected(false);
          setError('Failed to subscribe to channel');
          console.error(`❌ Failed to subscribe to channel: ${channelName}`);
        } else if (status === 'TIMED_OUT') {
          setIsConnected(false);
          setError('Connection timed out');
          console.error(`⏰ Connection timed out for channel: ${channelName}`);
        } else if (status === 'CLOSED') {
          setIsConnected(false);
          console.log(`🔒 Channel closed: ${channelName}`);
        }
      });

      channelRef.current = channel;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      console.error('Error subscribing to channel:', err);
    }
  }, [channelName, options, handleEvent]);

  const unsubscribe = useCallback(() => {
    if (channelRef.current) {
      channelRef.current.unsubscribe();
      channelRef.current = null;
      setIsConnected(false);
      console.log(`🔌 Unsubscribed from channel: ${channelName}`);
    }
  }, [channelName]);

  useEffect(() => {
    subscribe();

    return () => {
      unsubscribe();
    };
  }, [subscribe, unsubscribe]);

  return {
    data,
    isConnected,
    error,
    subscribe,
    unsubscribe,
    channel: channelRef.current,
  };
};

/**
 * Hook đơn giản để listen database changes
 */
export const useRealtimeTable = <T = any>(
  table: string,
  onInsert?: (record: T) => void,
  onUpdate?: (record: T) => void,
  onDelete?: (record: T) => void
) => {
  return useRealtime<T>(
    `table-${table}`,
    { table, event: '*' },
    (event) => {
      switch (event.eventType) {
        case 'INSERT':
          onInsert?.(event.new);
          break;
        case 'UPDATE':
          onUpdate?.(event.new);
          break;
        case 'DELETE':
          onDelete?.(event.old);
          break;
      }
    }
  );
};

/**
 * Hook để broadcast messages
 */
export const useRealtimeBroadcast = (channelName: string) => {
  const channelRef = useRef<RealtimeChannel | null>(null);
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    const channel = supabase.channel(channelName);
    
    channel.subscribe((status) => {
      setIsConnected(status === 'SUBSCRIBED');
    });

    channelRef.current = channel;

    return () => {
      channel.unsubscribe();
    };
  }, [channelName]);

  const broadcast = useCallback((event: string, payload: any) => {
    if (channelRef.current && isConnected) {
      channelRef.current.send({
        type: 'broadcast',
        event,
        payload,
      });
    }
  }, [isConnected]);

  return { broadcast, isConnected };
};

/**
 * Hook để track user presence
 */
export const useRealtimePresence = (channelName: string, userInfo: any) => {
  const channelRef = useRef<RealtimeChannel | null>(null);
  const [presenceState, setPresenceState] = useState<any>({});
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    const channel = supabase.channel(channelName);

    channel
      .on('presence', { event: 'sync' }, () => {
        const newState = channel.presenceState();
        setPresenceState(newState);
      })
      .on('presence', { event: 'join' }, ({ key, newPresences }) => {
        console.log('User joined:', key, newPresences);
      })
      .on('presence', { event: 'leave' }, ({ key, leftPresences }) => {
        console.log('User left:', key, leftPresences);
      })
      .subscribe(async (status) => {
        if (status === 'SUBSCRIBED') {
          setIsConnected(true);
          // Track user presence
          await channel.track(userInfo);
        }
      });

    channelRef.current = channel;

    return () => {
      channel.unsubscribe();
    };
  }, [channelName, userInfo]);

  return { presenceState, isConnected };
};

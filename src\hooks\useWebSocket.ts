'use client';

import { useEffect, useRef, useState, useCallback } from 'react';
import { Client, IMessage, StompConfig, StompSubscription } from '@stomp/stompjs';
import SockJS from 'sockjs-client';

// Types cho WebSocket events
export interface WebSocketMessage<T = any> {
  destination: string;
  body: T;
  headers?: Record<string, string>;
  timestamp: number;
}

export interface WebSocketSubscription {
  destination: string;
  callback: (message: WebSocketMessage) => void;
  subscription?: StompSubscription;
}

export interface WebSocketOptions {
  url: string;
  reconnectDelay?: number;
  heartbeatIncoming?: number;
  heartbeatOutgoing?: number;
  debug?: boolean;
  headers?: Record<string, string>;
}

export interface WebSocketHookReturn {
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
  lastMessage: WebSocketMessage | null;
  subscribe: (destination: string, callback: (message: WebSocketMessage) => void) => () => void;
  unsubscribe: (destination: string) => void;
  sendMessage: (destination: string, body: any, headers?: Record<string, string>) => void;
  connect: () => void;
  disconnect: () => void;
  client: Client | null;
}

/**
 * Hook chính để xử lý WebSocket với STOMP/SockJS
 * @param options - Các tùy chọn cấu hình WebSocket
 */
export const useWebSocket = (options: WebSocketOptions): WebSocketHookReturn => {
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastMessage, setLastMessage] = useState<WebSocketMessage | null>(null);
  
  const clientRef = useRef<Client | null>(null);
  const subscriptionsRef = useRef<Map<string, WebSocketSubscription>>(new Map());
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Tạo STOMP client
  const createClient = useCallback(() => {
    const client = new Client({
      webSocketFactory: () => new SockJS(options.url),
      reconnectDelay: options.reconnectDelay || 5000,
      heartbeatIncoming: options.heartbeatIncoming || 4000,
      heartbeatOutgoing: options.heartbeatOutgoing || 4000,
      debug: options.debug ? (str) => console.log('STOMP Debug:', str) : undefined,
      connectHeaders: options.headers || {},
      
      onConnect: () => {
        console.log('WebSocket connected');
        setIsConnected(true);
        setIsConnecting(false);
        setError(null);
        
        // Resubscribe to all destinations
        subscriptionsRef.current.forEach((sub, destination) => {
          if (!sub.subscription) {
            const subscription = client.subscribe(destination, (message: IMessage) => {
              const parsedMessage: WebSocketMessage = {
                destination: message.headers.destination || destination,
                body: JSON.parse(message.body),
                headers: message.headers,
                timestamp: Date.now(),
              };
              setLastMessage(parsedMessage);
              sub.callback(parsedMessage);
            });
            sub.subscription = subscription;
          }
        });
      },
      
      onDisconnect: () => {
        console.log('WebSocket disconnected');
        setIsConnected(false);
        setIsConnecting(false);
        
        // Clear all subscriptions
        subscriptionsRef.current.forEach((sub) => {
          sub.subscription = undefined;
        });
      },
      
      onStompError: (frame) => {
        console.error('STOMP Error:', frame);
        setError(`STOMP Error: ${frame.headers.message || 'Unknown error'}`);
        setIsConnected(false);
        setIsConnecting(false);
      },
      
      onWebSocketError: (event) => {
        console.error('WebSocket Error:', event);
        setError('WebSocket connection error');
        setIsConnected(false);
        setIsConnecting(false);
      },
    });

    return client;
  }, [options]);

  // Kết nối WebSocket
  const connect = useCallback(() => {
    if (clientRef.current?.connected || isConnecting) {
      return;
    }

    setIsConnecting(true);
    setError(null);

    try {
      if (!clientRef.current) {
        clientRef.current = createClient();
      }
      clientRef.current.activate();
    } catch (err) {
      console.error('Failed to connect:', err);
      setError('Failed to connect to WebSocket');
      setIsConnecting(false);
    }
  }, [createClient, isConnecting]);

  // Ngắt kết nối WebSocket
  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (clientRef.current) {
      clientRef.current.deactivate();
      clientRef.current = null;
    }

    subscriptionsRef.current.clear();
    setIsConnected(false);
    setIsConnecting(false);
    setError(null);
  }, []);

  // Subscribe to destination
  const subscribe = useCallback((
    destination: string,
    callback: (message: WebSocketMessage) => void
  ): (() => void) => {
    const subscription: WebSocketSubscription = {
      destination,
      callback,
    };

    subscriptionsRef.current.set(destination, subscription);

    // Nếu đã kết nối, subscribe ngay lập tức
    if (clientRef.current?.connected) {
      const stompSubscription = clientRef.current.subscribe(destination, (message: IMessage) => {
        const parsedMessage: WebSocketMessage = {
          destination: message.headers.destination || destination,
          body: JSON.parse(message.body),
          headers: message.headers,
          timestamp: Date.now(),
        };
        setLastMessage(parsedMessage);
        callback(parsedMessage);
      });
      subscription.subscription = stompSubscription;
    }

    // Return unsubscribe function
    return () => unsubscribe(destination);
  }, []);

  // Unsubscribe from destination
  const unsubscribe = useCallback((destination: string) => {
    const subscription = subscriptionsRef.current.get(destination);
    if (subscription?.subscription) {
      subscription.subscription.unsubscribe();
    }
    subscriptionsRef.current.delete(destination);
  }, []);

  // Gửi message
  const sendMessage = useCallback((
    destination: string,
    body: any,
    headers: Record<string, string> = {}
  ) => {
    if (!clientRef.current?.connected) {
      console.warn('WebSocket not connected. Cannot send message.');
      return;
    }

    try {
      clientRef.current.publish({
        destination,
        body: JSON.stringify(body),
        headers,
      });
    } catch (err) {
      console.error('Failed to send message:', err);
      setError('Failed to send message');
    }
  }, []);

  // Auto connect on mount
  useEffect(() => {
    connect();

    return () => {
      disconnect();
    };
  }, [connect, disconnect]);

  // Auto reconnect on connection loss
  useEffect(() => {
    if (!isConnected && !isConnecting && !error && clientRef.current) {
      reconnectTimeoutRef.current = setTimeout(() => {
        console.log('Attempting to reconnect...');
        connect();
      }, options.reconnectDelay || 5000);
    }

    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
        reconnectTimeoutRef.current = null;
      }
    };
  }, [isConnected, isConnecting, error, connect, options.reconnectDelay]);

  return {
    isConnected,
    isConnecting,
    error,
    lastMessage,
    subscribe,
    unsubscribe,
    sendMessage,
    connect,
    disconnect,
    client: clientRef.current,
  };
};

/**
 * Hook đơn giản để subscribe một destination cụ thể
 */
export const useWebSocketSubscription = <T = any>(
  url: string,
  destination: string,
  options?: Omit<WebSocketOptions, 'url'>
) => {
  const [messages, setMessages] = useState<WebSocketMessage<T>[]>([]);
  const [lastMessage, setLastMessage] = useState<WebSocketMessage<T> | null>(null);

  const webSocket = useWebSocket({ url, ...options });

  useEffect(() => {
    if (!destination) return;

    const unsubscribe = webSocket.subscribe(destination, (message) => {
      const typedMessage = message as WebSocketMessage<T>;
      setLastMessage(typedMessage);
      setMessages(prev => [...prev, typedMessage]);
    });

    return unsubscribe;
  }, [destination, webSocket.subscribe]);

  const clearMessages = useCallback(() => {
    setMessages([]);
    setLastMessage(null);
  }, []);

  return {
    ...webSocket,
    messages,
    lastMessage,
    clearMessages,
  };
};

'use client';

import { useCallback, useEffect, useState } from 'react';
import { useWebSocket, WebSocketMessage, WebSocketOptions } from './useWebSocket';

// Types cho các utility hooks
export interface ChatMessage {
  id: string;
  userId: string;
  username: string;
  message: string;
  timestamp: number;
  type?: 'text' | 'image' | 'file';
}

export interface NotificationMessage {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  timestamp: number;
  read?: boolean;
}

export interface PresenceUser {
  userId: string;
  username: string;
  status: 'online' | 'away' | 'busy' | 'offline';
  lastSeen: number;
}

/**
 * Hook để xử lý chat realtime
 */
export const useWebSocketChat = (
  url: string,
  roomId: string,
  userId: string,
  username: string,
  options?: Omit<WebSocketOptions, 'url'>
) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [onlineUsers, setOnlineUsers] = useState<PresenceUser[]>([]);
  const [isTyping, setIsTyping] = useState<string[]>([]);

  const webSocket = useWebSocket({ url, ...options });

  // Subscribe to chat messages
  useEffect(() => {
    if (!roomId) return;

    const unsubscribeMessages = webSocket.subscribe(
      `/topic/chat/${roomId}`,
      (message: WebSocketMessage<ChatMessage>) => {
        setMessages(prev => [...prev, message.body]);
      }
    );

    const unsubscribePresence = webSocket.subscribe(
      `/topic/presence/${roomId}`,
      (message: WebSocketMessage<{ users: PresenceUser[] }>) => {
        setOnlineUsers(message.body.users);
      }
    );

    const unsubscribeTyping = webSocket.subscribe(
      `/topic/typing/${roomId}`,
      (message: WebSocketMessage<{ userId: string; username: string; isTyping: boolean }>) => {
        const { userId: typingUserId, isTyping: typing } = message.body;
        setIsTyping(prev => {
          if (typing && !prev.includes(typingUserId)) {
            return [...prev, typingUserId];
          } else if (!typing) {
            return prev.filter(id => id !== typingUserId);
          }
          return prev;
        });
      }
    );

    return () => {
      unsubscribeMessages();
      unsubscribePresence();
      unsubscribeTyping();
    };
  }, [roomId, webSocket.subscribe]);

  // Join room when connected
  useEffect(() => {
    if (webSocket.isConnected && roomId) {
      webSocket.sendMessage('/app/chat/join', {
        roomId,
        userId,
        username,
      });
    }
  }, [webSocket.isConnected, roomId, userId, username, webSocket.sendMessage]);

  // Send chat message
  const sendMessage = useCallback((message: string, type: ChatMessage['type'] = 'text') => {
    if (!webSocket.isConnected || !message.trim()) return;

    const chatMessage: Omit<ChatMessage, 'id' | 'timestamp'> = {
      userId,
      username,
      message: message.trim(),
      type,
    };

    webSocket.sendMessage(`/app/chat/${roomId}`, chatMessage);
  }, [webSocket.isConnected, webSocket.sendMessage, roomId, userId, username]);

  // Send typing indicator
  const sendTyping = useCallback((isTyping: boolean) => {
    if (!webSocket.isConnected) return;

    webSocket.sendMessage(`/app/typing/${roomId}`, {
      userId,
      username,
      isTyping,
    });
  }, [webSocket.isConnected, webSocket.sendMessage, roomId, userId, username]);

  // Leave room
  const leaveRoom = useCallback(() => {
    if (webSocket.isConnected && roomId) {
      webSocket.sendMessage('/app/chat/leave', {
        roomId,
        userId,
      });
    }
  }, [webSocket.isConnected, webSocket.sendMessage, roomId, userId]);

  return {
    ...webSocket,
    messages,
    onlineUsers,
    isTyping,
    sendMessage,
    sendTyping,
    leaveRoom,
  };
};

/**
 * Hook để xử lý notifications realtime
 */
export const useWebSocketNotifications = (
  url: string,
  userId: string,
  options?: Omit<WebSocketOptions, 'url'>
) => {
  const [notifications, setNotifications] = useState<NotificationMessage[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);

  const webSocket = useWebSocket({ url, ...options });

  // Subscribe to user notifications
  useEffect(() => {
    if (!userId) return;

    const unsubscribe = webSocket.subscribe(
      `/user/${userId}/notifications`,
      (message: WebSocketMessage<NotificationMessage>) => {
        const notification = { ...message.body, read: false };
        setNotifications(prev => [notification, ...prev]);
        setUnreadCount(prev => prev + 1);
      }
    );

    return unsubscribe;
  }, [userId, webSocket.subscribe]);

  // Mark notification as read
  const markAsRead = useCallback((notificationId: string) => {
    setNotifications(prev =>
      prev.map(notif =>
        notif.id === notificationId ? { ...notif, read: true } : notif
      )
    );
    setUnreadCount(prev => Math.max(0, prev - 1));
  }, []);

  // Mark all as read
  const markAllAsRead = useCallback(() => {
    setNotifications(prev =>
      prev.map(notif => ({ ...notif, read: true }))
    );
    setUnreadCount(0);
  }, []);

  // Clear notifications
  const clearNotifications = useCallback(() => {
    setNotifications([]);
    setUnreadCount(0);
  }, []);

  return {
    ...webSocket,
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    clearNotifications,
  };
};

/**
 * Hook để xử lý live updates cho data
 */
export const useWebSocketLiveData = <T = any>(
  url: string,
  dataType: string,
  options?: Omit<WebSocketOptions, 'url'>
) => {
  const [data, setData] = useState<T[]>([]);
  const [lastUpdate, setLastUpdate] = useState<{
    type: 'CREATE' | 'UPDATE' | 'DELETE';
    item: T;
    timestamp: number;
  } | null>(null);

  const webSocket = useWebSocket({ url, ...options });

  // Subscribe to data updates
  useEffect(() => {
    if (!dataType) return;

    const unsubscribe = webSocket.subscribe(
      `/topic/data/${dataType}`,
      (message: WebSocketMessage<{
        type: 'CREATE' | 'UPDATE' | 'DELETE';
        item: T;
        id?: string;
      }>) => {
        const { type, item, id } = message.body;
        
        setLastUpdate({
          type,
          item,
          timestamp: message.timestamp,
        });

        setData(prev => {
          switch (type) {
            case 'CREATE':
              return [...prev, item];
            case 'UPDATE':
              return prev.map(existingItem => 
                (existingItem as any).id === (item as any).id ? item : existingItem
              );
            case 'DELETE':
              return prev.filter(existingItem => 
                (existingItem as any).id !== id
              );
            default:
              return prev;
          }
        });
      }
    );

    return unsubscribe;
  }, [dataType, webSocket.subscribe]);

  // Request initial data
  const requestInitialData = useCallback(() => {
    if (webSocket.isConnected) {
      webSocket.sendMessage(`/app/data/${dataType}/request`, {});
    }
  }, [webSocket.isConnected, webSocket.sendMessage, dataType]);

  // Refresh data
  const refreshData = useCallback(() => {
    setData([]);
    requestInitialData();
  }, [requestInitialData]);

  return {
    ...webSocket,
    data,
    lastUpdate,
    requestInitialData,
    refreshData,
  };
};

/**
 * Hook để xử lý presence/status của users
 */
export const useWebSocketPresence = (
  url: string,
  roomId: string,
  userInfo: { userId: string; username: string; status?: string },
  options?: Omit<WebSocketOptions, 'url'>
) => {
  const [presenceList, setPresenceList] = useState<PresenceUser[]>([]);
  const [userCount, setUserCount] = useState(0);

  const webSocket = useWebSocket({ url, ...options });

  // Subscribe to presence updates
  useEffect(() => {
    if (!roomId) return;

    const unsubscribe = webSocket.subscribe(
      `/topic/presence/${roomId}`,
      (message: WebSocketMessage<{
        type: 'JOIN' | 'LEAVE' | 'UPDATE' | 'SYNC';
        users: PresenceUser[];
        user?: PresenceUser;
      }>) => {
        const { type, users, user } = message.body;

        switch (type) {
          case 'SYNC':
            setPresenceList(users);
            setUserCount(users.length);
            break;
          case 'JOIN':
            if (user) {
              setPresenceList(prev => {
                const exists = prev.find(u => u.userId === user.userId);
                if (!exists) {
                  return [...prev, user];
                }
                return prev;
              });
              setUserCount(prev => prev + 1);
            }
            break;
          case 'LEAVE':
            if (user) {
              setPresenceList(prev => prev.filter(u => u.userId !== user.userId));
              setUserCount(prev => Math.max(0, prev - 1));
            }
            break;
          case 'UPDATE':
            if (user) {
              setPresenceList(prev =>
                prev.map(u => u.userId === user.userId ? user : u)
              );
            }
            break;
        }
      }
    );

    return unsubscribe;
  }, [roomId, webSocket.subscribe]);

  // Join presence when connected
  useEffect(() => {
    if (webSocket.isConnected && roomId) {
      webSocket.sendMessage(`/app/presence/${roomId}/join`, userInfo);
    }
  }, [webSocket.isConnected, roomId, userInfo, webSocket.sendMessage]);

  // Update user status
  const updateStatus = useCallback((status: string) => {
    if (webSocket.isConnected && roomId) {
      webSocket.sendMessage(`/app/presence/${roomId}/update`, {
        ...userInfo,
        status,
      });
    }
  }, [webSocket.isConnected, webSocket.sendMessage, roomId, userInfo]);

  // Leave presence
  const leavePresence = useCallback(() => {
    if (webSocket.isConnected && roomId) {
      webSocket.sendMessage(`/app/presence/${roomId}/leave`, {
        userId: userInfo.userId,
      });
    }
  }, [webSocket.isConnected, webSocket.sendMessage, roomId, userInfo.userId]);

  return {
    ...webSocket,
    presenceList,
    userCount,
    updateStatus,
    leavePresence,
  };
};
